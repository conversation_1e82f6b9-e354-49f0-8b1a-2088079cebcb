package br.com.pacto.controller.json.agendamento;

import br.com.pacto.bean.Agendamento;
import br.com.pacto.bean.colaborador.ColaboradorSimplesTO;
import br.com.pacto.objeto.Calendario;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AgendaDisponibilidadeDTO {

    private Integer id;
    private String dia;
    private ColaboradorSimplesTO professor;
    private String horarioInicial;
    private String horarioFinal;
    private Date inicioDate;
    private Date fimDate;
    private Integer agendaDisponibilidadeConfigId;
    private List<TipoAtividadeDTO> tiposAtividades;
    private String ambiente;
    private Integer ambienteId;
    private String descricao;
    private String tipoHorario;
    private Integer horarioDisponibilidadeCod;
    private Boolean bloqueado = Boolean.FALSE;
    private Integer codigoZwAmbiente;

    public AgendaDisponibilidadeDTO() {

    }

    public AgendaDisponibilidadeDTO(Agendamento agendamento) {
        this.id = agendamento.getCodigo();
        this.dia = Calendario.getData(agendamento.getInicio(), "yyyyMMdd");
        this.tiposAtividades = (agendamento.getTipoEvento() != null && agendamento.getTipoEvento().getCodigo() != null) ? new ArrayList(){{
            add(new TipoAtividadeDTO(agendamento.getTipoEvento()));
        }} : new ArrayList(){{
            add(new TipoAtividadeDTO(agendamento.getHorarioDisponibilidade().getDisponibilidade()));
        }};
        this.inicioDate = agendamento.getInicio();
        this.fimDate = agendamento.getFim();
        this.horarioInicial = agendamento.getHoraInicioApresentar();
        this.horarioFinal = agendamento.getHoraFimApresentar();
        this.professor = new ColaboradorSimplesTO(agendamento.getProfessor(), true);
        if(agendamento.getHorarioDisponibilidade() != null) {
            this.horarioDisponibilidadeCod = agendamento.getHorarioDisponibilidade().getCodigo();
        }
    }

    public AgendaDisponibilidadeDTO clone() {
        AgendaDisponibilidadeDTO clone = new  AgendaDisponibilidadeDTO();
        clone.id = this.id;
        clone.dia = this.dia;
        clone.tiposAtividades = new ArrayList(this.tiposAtividades);
        clone.inicioDate = this.inicioDate;
        clone.fimDate = this.fimDate;
        clone.horarioInicial = this.horarioInicial;
        clone.horarioFinal = this.horarioFinal;
        clone.professor = this.professor;
        clone.horarioDisponibilidadeCod = this.horarioDisponibilidadeCod;
        return clone;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDia() {
        return dia;
    }

    public void setDia(String dia) {
        this.dia = dia;
    }

    public ColaboradorSimplesTO getProfessor() {
        return professor;
    }

    public void setProfessor(ColaboradorSimplesTO professor) {
        this.professor = professor;
    }

    public String getHorarioInicial() {
        return horarioInicial;
    }

    public void setHorarioInicial(String horarioInicial) {
        this.horarioInicial = horarioInicial;
    }

    public String getHorarioFinal() {
        return horarioFinal;
    }

    public void setHorarioFinal(String horarioFinal) {
        this.horarioFinal = horarioFinal;
    }

    public Integer getAgendaDisponibilidadeConfigId() {
        return agendaDisponibilidadeConfigId;
    }

    public void setAgendaDisponibilidadeConfigId(Integer agendaDisponibilidadeConfigId) {
        this.agendaDisponibilidadeConfigId = agendaDisponibilidadeConfigId;
    }

    public List<TipoAtividadeDTO> getTiposAtividades() {
        return tiposAtividades;
    }

    public void setTiposAtividades(List<TipoAtividadeDTO> tiposAtividades) {
        this.tiposAtividades = tiposAtividades;
    }

    public Date getInicioDate() {
        return inicioDate;
    }

    public void setInicioDate(Date inicioDate) {
        this.inicioDate = inicioDate;
    }

    public Date getFimDate() {
        return fimDate;
    }

    public void setFimDate(Date fimDate) {
        this.fimDate = fimDate;
    }

    public TipoAtividadeDTO getTipo(){
        try {
            return getTiposAtividades().get(0);
        }catch (Exception e){
            return new TipoAtividadeDTO();
        }
    }

    public String getAmbiente() {
        return ambiente;
    }

    public void setAmbiente(String ambiente) {
        this.ambiente = ambiente;
    }

    public String getTipoHorario() {
        return tipoHorario;
    }

    public void setTipoHorario(String tipoHorario) {
        this.tipoHorario = tipoHorario;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getHorarioDisponibilidadeCod() {
        return horarioDisponibilidadeCod;
    }

    public void setHorarioDisponibilidadeCod(Integer horarioDisponibilidadeCod) {
        this.horarioDisponibilidadeCod = horarioDisponibilidadeCod;
    }

    public Boolean getBloqueado() {
        return bloqueado;
    }

    public void setBloqueado(Boolean bloqueado) {
        this.bloqueado = bloqueado;
    }

    public Integer getCodigoZwAmbiente() {
        return codigoZwAmbiente;
    }

    public void setCodigoZwAmbiente(Integer codigoZwAmbiente) {
        this.codigoZwAmbiente = codigoZwAmbiente;
    }

    public Integer getAmbienteId() {
        return ambienteId;
    }

    public void setAmbienteId(Integer ambienteId) {
        this.ambienteId = ambienteId;
    }
}
